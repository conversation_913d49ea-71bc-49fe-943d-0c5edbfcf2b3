import type {SettingsSplitNavigatorParamList} from '@libs/Navigation/types';
import SCREENS from '@src/SCREENS';

// This file is used to define relation between settings split navigator's central screens and RHP screens.
const SETTINGS_TO_RHP: Partial<Record<keyof SettingsSplitNavigatorParamList, string[]>> = {
    [SCREENS.SETTINGS.PROFILE.ROOT]: [
        SCREENS.SETTINGS.PROFILE.DISPLAY_NAME,
        SCREENS.SETTINGS.PROFILE.CONTACT_METHODS,
        SCREENS.SETTINGS.PROFILE.CONTACT_METHOD_DETAILS,
        SCREENS.SETTINGS.PROFILE.NEW_CONTACT_METHOD,
        SCREENS.SETTINGS.PROFILE.STATUS_CLEAR_AFTER,
        SCREENS.SETTINGS.PROFILE.STATUS_CLEAR_AFTER_DATE,
        SCREENS.SETTINGS.PROFILE.STATUS_CLEAR_AFTER_TIME,
        SCREENS.SETTINGS.PROFILE.STATUS,
        SCREENS.SETTINGS.PROFILE.PRONOUNS,
        SCREENS.SETTINGS.PROFILE.TIMEZONE,
        SCREENS.SETTINGS.PROFILE.TIMEZONE_SELECT,
        SCREENS.SETTINGS.PROFILE.LEGAL_NAME,
        SCREENS.SETTINGS.PROFILE.DATE_OF_BIRTH,
        SCREENS.SETTINGS.PROFILE.PHONE_NUMBER,
        SCREENS.SETTINGS.PROFILE.ADDRESS,
        SCREENS.SETTINGS.PROFILE.ADDRESS_COUNTRY,
        SCREENS.SETTINGS.SHARE_CODE,
        SCREENS.SETTINGS.EXIT_SURVEY.REASON,
        SCREENS.SETTINGS.EXIT_SURVEY.RESPONSE,
        SCREENS.SETTINGS.EXIT_SURVEY.CONFIRM,
    ],
    [SCREENS.SETTINGS.PREFERENCES.ROOT]: [
        SCREENS.SETTINGS.PREFERENCES.PRIORITY_MODE,
        SCREENS.SETTINGS.PREFERENCES.LANGUAGE,
        SCREENS.SETTINGS.PREFERENCES.THEME,
        SCREENS.SETTINGS.PREFERENCES.PAYMENT_CURRENCY,
    ],
    [SCREENS.SETTINGS.WALLET.ROOT]: [
        SCREENS.SETTINGS.WALLET.DOMAIN_CARD,
        SCREENS.SETTINGS.WALLET.TRANSFER_BALANCE,
        SCREENS.SETTINGS.WALLET.CHOOSE_TRANSFER_ACCOUNT,
        SCREENS.SETTINGS.WALLET.ENABLE_PAYMENTS,
        SCREENS.SETTINGS.WALLET.CARD_ACTIVATE,
        SCREENS.SETTINGS.WALLET.REPORT_VIRTUAL_CARD_FRAUD,
        SCREENS.SETTINGS.WALLET.REPORT_VIRTUAL_CARD_FRAUD_CONFIRMATION,
        SCREENS.SETTINGS.WALLET.CARDS_DIGITAL_DETAILS_UPDATE_ADDRESS,
        SCREENS.SETTINGS.REPORT_CARD_LOST_OR_DAMAGED,
    ],
    [SCREENS.SETTINGS.SECURITY]: [
        SCREENS.TWO_FACTOR_AUTH.ROOT,
        SCREENS.TWO_FACTOR_AUTH.VERIFY,
        SCREENS.TWO_FACTOR_AUTH.SUCCESS,
        SCREENS.TWO_FACTOR_AUTH.DISABLED,
        SCREENS.TWO_FACTOR_AUTH.DISABLE,
        SCREENS.SETTINGS.CLOSE,
        SCREENS.SETTINGS.LOCK.LOCK_ACCOUNT,
        SCREENS.SETTINGS.LOCK.UNLOCK_ACCOUNT,
        SCREENS.SETTINGS.LOCK.FAILED_TO_LOCK_ACCOUNT,
        SCREENS.SETTINGS.DELEGATE.ADD_DELEGATE,
        SCREENS.SETTINGS.DELEGATE.DELEGATE_ROLE,
        SCREENS.SETTINGS.DELEGATE.UPDATE_DELEGATE_ROLE,
        SCREENS.SETTINGS.DELEGATE.DELEGATE_CONFIRM,
        SCREENS.SETTINGS.MERGE_ACCOUNTS.ACCOUNT_DETAILS,
        SCREENS.SETTINGS.MERGE_ACCOUNTS.ACCOUNT_VALIDATE,
        SCREENS.SETTINGS.MERGE_ACCOUNTS.MERGE_RESULT,
    ],
    [SCREENS.SETTINGS.ABOUT]: [SCREENS.SETTINGS.APP_DOWNLOAD_LINKS],
    [SCREENS.SETTINGS.SAVE_THE_WORLD]: [SCREENS.I_KNOW_A_TEACHER, SCREENS.INTRO_SCHOOL_PRINCIPAL, SCREENS.I_AM_A_TEACHER],
    [SCREENS.SETTINGS.TROUBLESHOOT]: [SCREENS.SETTINGS.CONSOLE],
    [SCREENS.SETTINGS.SUBSCRIPTION.ROOT]: [
        SCREENS.SETTINGS.SUBSCRIPTION.ADD_PAYMENT_CARD,
        SCREENS.SETTINGS.SUBSCRIPTION.SIZE,
        SCREENS.SETTINGS.SUBSCRIPTION.DISABLE_AUTO_RENEW_SURVEY,
        SCREENS.SETTINGS.SUBSCRIPTION.REQUEST_EARLY_CANCELLATION,
        SCREENS.SETTINGS.SUBSCRIPTION.CHANGE_BILLING_CURRENCY,
        SCREENS.SETTINGS.SUBSCRIPTION.CHANGE_PAYMENT_CURRENCY,
        SCREENS.SETTINGS.SUBSCRIPTION.SETTINGS_DETAILS,
    ],
};

export default SETTINGS_TO_RHP;

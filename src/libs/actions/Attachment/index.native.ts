import RNFetchBlob from 'react-native-blob-util';
import Onyx from 'react-native-onyx';
import type {OnyxCollection} from 'react-native-onyx';
import ONYXKEYS from '@src/ONYXKEYS';
import type {Attachment} from '@src/types/onyx';

let attachments: OnyxCollection<Attachment> | undefined;
Onyx.connect({
    key: ONYXKEYS.COLLECTION.ATTACHMENT,
    waitForCollectionCallback: true,
    callback: (value) => (attachments = value ?? {}),
});

function storeAttachment(attachmentID: string, uri: string, reportId: string) {
    if (!attachmentID || !uri) {
        return;
    }
    if (uri.startsWith('file://')) {
        Onyx.set(`${ONYXKEYS.COLLECTION.ATTACHMENT}${reportId}`, {
            attachmentID,
            source: uri,
        });
        return;
    }

    const attachment = attachments?.[attachmentID];

    if (attachment?.source && attachment.remoteSource === uri) {
        return;
    }

    RNFetchBlob.config({fileCache: true, path: `${RNFetchBlob.fs.dirs.DocumentDir}/${attachmentID}`})
        .fetch('GET', uri)
        .then((response) => {
            const filePath = response.path();
            Onyx.set(`${ONYXKEYS.COLLECTION.ATTACHMENT}${reportId}`, {
                attachmentID,
                source: `file://${filePath}`,
                remoteSource: uri,
            });
        })
        .catch(() => {
            throw new Error('Failed to store attachment');
        });
}

function getAttachmentSource(attachmentID: string, currentSource: string, reportId: string) {
    if (!attachmentID) {
        return;
    }
    const attachment = attachments?.[`${ONYXKEYS.COLLECTION.ATTACHMENT}${reportId}`];

    if (attachment?.remoteSource && attachment.remoteSource !== currentSource) {
        storeAttachment(attachmentID, currentSource, reportId);
        return currentSource;
    }
    return attachment?.source;
}

function deleteAttachment(attachmentID: string, reportId: string) {
    if (!attachmentID) {
        return;
    }
    Onyx.set(`${ONYXKEYS.COLLECTION.ATTACHMENT}${reportId}`, null);
}

export {storeAttachment, getAttachmentSource, deleteAttachment};

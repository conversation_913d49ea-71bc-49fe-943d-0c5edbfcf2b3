<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.chat.expensify.backgroundTaskSync</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>9.1.63</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>new-expensify</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.921154746561-s3uqn2oe4m85tufi6mqflbfbuajrm2i3</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>********</string>
	<key>FullStory</key>
	<dict>
		<key>OrgId</key>
		<string>o-1WN56P-na1</string>
		<key>RecordOnStart</key>
		<false/>
	</dict>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>venmo</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Your camera is used to create chat attachments, documents, and facial capture.</string>
	<key>NSContactsUsageDescription</key>
	<string>Import contacts from your phone so your favorite people are always a tap away.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Your location is used to determine your default currency and timezone.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Your location is used to determine your default currency and timezone.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Required for video capture</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Your camera roll is used to store chat attachments.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Your photos are used to create chat attachments.</string>
	<key>NSUserActivityTypes</key>
	<array>
		<string>INSendMessageIntent</string>
	</array>
	<key>UIAppFonts</key>
	<array>
		<string>ExpensifyNewKansas-Medium.otf</string>
		<string>ExpensifyNewKansas-MediumItalic.otf</string>
		<string>ExpensifyMono-Bold.otf</string>
		<string>ExpensifyMono-BoldItalic.otf</string>
		<string>ExpensifyMono-Italic.otf</string>
		<string>ExpensifyMono-Regular.otf</string>
		<string>ExpensifyNeue-Bold.otf</string>
		<string>ExpensifyNeue-BoldItalic.otf</string>
		<string>ExpensifyNeue-Italic.otf</string>
		<string>ExpensifyNeue-Regular.otf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
		<string>processing</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>BootSplash</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <title>New Expensify</title>
    <meta name="description" content="Corporate cards, reimbursements, receipt scanning, invoicing, and bill pay.">
    <meta name="theme-color" content="#03D47C">
    <meta property="twitter:card" content="summary">
    <meta property="twitter:site" content="@expensify">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="New Expensify">
    <meta property="og:url" content="https://new.expensify.com">
    <meta property="og:title" content="New Expensify">
    <meta property="og:description" content="Corporate cards, reimbursements, receipt scanning, invoicing, and bill pay.">
    <meta property="og:image" content="https://new.expensify.com/og-preview-image.png">
    <link rel="stylesheet" type="text/css" href="/css/fonts.css" />
    <link rel="stylesheet" type="text/css" href="/css/pdf.css" />
    <!-- Do not index the staging site -->
    <% if (htmlWebpackPlugin.options.isStaging) { %>
        <meta name="robots" content="noindex, nofollow">
    <% } %>

    <% if (htmlWebpackPlugin.options.isWeb && (htmlWebpackPlugin.options.isStaging || htmlWebpackPlugin.options.isProduction)) { %>
        <!-- begin Convert Experiences code-->
        <script type="text/javascript" src="//cdn-4.convertexperiments.com/v1/js/10042537-100413459.js">
        </script>
        <!-- end Convert Experiences code -->
    <% } %>

    <style>
        .signin-button {
            width: 100% !important;
            height: 100% !important;
        }
        html,
        body,
        #root,
        #root > div,
        #root > div > div {
            height: 100% !important;
        }
        * {
            touch-action: pan-x pan-y;
        }
        body {
           overflow: hidden;
           touch-action: none;
        }
        [data-drag-area='true'] {
            -webkit-app-region: drag;
        }
        [data-drag-area='false'], [data-drag-area='true'] [data-tag="pressable"] {
            -webkit-app-region: no-drag;

        }
        input::placeholder {
            user-select: none;
            -webkit-user-select: none
        }
        .disable-select * {
            -webkit-user-select: none !important;
            -webkit-touch-callout: none !important;
        }
        :focus-visible {
            outline: 0;
            box-shadow: inset 0px 0px 0px 1px #5AB0FF;
        }
        :focus-visible[data-inner-box-shadow-element]{
            overflow: hidden;
        }
        :focus-visible[data-inner-box-shadow-element]::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            box-shadow: inset 0px 0px 0px 1px #5AB0FF;
            pointer-events: none;
            z-index: 1000;
        }
        :focus[data-focusvisible-polyfill] {
            outline: 0;
            box-shadow: inset 0px 0px 0px 1px #5AB0FF;
        }
        input:focus-visible, input:focus[data-focusvisible-polyfill],
        select:focus-visible, select:focus[data-focusvisible-polyfill]  {
            box-shadow: none;
        }
        ::-ms-reveal {
            display: none;
        }

        /* Customize Plaid iframe */
        [id^="plaid-link-iframe"] {
            color-scheme: dark !important;
        }

        /* Prevent autofill from overlapping with the input label in Chrome */
        div:has(input:-webkit-autofill, input[chrome-autofilled]) > label {
            transform: translateY(var(--active-label-translate-y)) scale(var(--active-label-scale)) !important;
            transition: transform var(--label-transition-duration);
        }

        .splash-logo > svg {
            width: 104px;
            height: 104px;
        }

        #splash {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            top: 0;
            background-color: #03D47C;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            transition-duration: 250ms;
            transition-property: opacity;
        }

        .animation {
            display: flex;
        }
    </style>
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1, interactive-widget=resizes-content, viewport-fit=cover">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="shortcut icon" id="favicon" href="/favicon.png">
    <% if (htmlWebpackPlugin.options.useThirdPartyScripts) { %>
        <!-- Google Tag Manager -->
        <script nonce='nonce-random-value'>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;var n=d.querySelector('[nonce]');
        n&&j.setAttribute('nonce',n.nonce||n.getAttribute('nonce'));f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-N4M3FLJZ');</script>
        <!-- End Google Tag Manager -->
    <% } %>
    <link rel="manifest" href="/manifest.json" />
</head>
<body>
    <% if (htmlWebpackPlugin.options.useThirdPartyScripts) { %>
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N4M3FLJZ" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
    <% } %>
    <div style="position: absolute; top: 0; left: 0; right: 0; height: 30px;" data-drag-area="true"></div>
    <div id="root" class="fs-unmask"></div>
    <div id="splash">
        <div class="splash-logo">
            <%= htmlWebpackPlugin.options.splashLogo %>
        </div>
    </div>
</body>
</html>

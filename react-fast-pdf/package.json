{"name": "react-fast-pdf", "version": "1.0.27", "description": "react-fast-pdf", "main": "./dist/index.js", "files": ["dist", "src"], "scripts": {"build": "tsc", "lint": "eslint src --fix", "ts": "tsc --noEmit", "prettier": "prettier --write .", "prettier-watch": "onchange \"**/*.js\" -- prettier --write --ignore-unknown {{changed}}", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Expensify/react-fast-pdf.git"}, "keywords": ["react-fast-pdf"], "author": "Expensify, Inc.", "license": "MIT", "bugs": {"url": "https://github.com/Expensify/react-fast-pdf/issues"}, "homepage": "https://github.com/Expensify/react-fast-pdf#readme", "dependencies": {"react-pdf": "9.2.0", "react-window": "^1.8.11"}, "peerDependencies": {"lodash": "4.x", "pdfjs-dist": "4.8.69", "react": "18.x", "react-dom": "18.x"}, "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@babel/preset-react": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@types/lodash": "^4.14.198", "@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "@types/react-window": "^1.8.5", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "babel-loader": "^9.1.3", "css-loader": "^6.8.1", "eslint": "^8.46.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.1", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.3", "onchange": "^7.1.0", "prettier": "^3.0.0", "style-loader": "^3.3.3", "ts-loader": "^9.4.4", "typescript": "^5.3.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}}
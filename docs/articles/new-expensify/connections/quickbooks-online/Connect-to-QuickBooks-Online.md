---
title: Connect to QuickBooks Online
description: Integrate QuickBooks Online with Expensify
keywords: [New Expensify, QuickBooks Online, QBO integration, connect QuickBooks, accounting sync, Expensify control plan]
order: 1
---

<div id="new-expensify" markdown="1">

To use the QuickBooks Online connection, you must have a QuickBooks Online account and an Expensify **Collect** plan. The **QuickBooks Self-Employed** subscription is not supported.

---

# Supported QuickBooks Online Features

Expensify's QuickBooks Online integration includes different features depending on your subscription level. While some options may appear in the app, you’ll see an error if your plan doesn’t support them.

| Feature                    | Simple Start | Essentials | Essentials Plus |
|----------------------------|--------------|------------|-----------------|
| Expense Reports            | ✅            | ✅          | ✅               |
| GL Accounts as Categories  | ✅            | ✅          | ✅               |
| Credit Card Transactions   | ✅            | ✅          | ✅               |
| Debit Card Transaction     |              | ✅          | ✅               |
| Classes                    |              | ✅          | ✅               |
| Customers                  |              | ✅          | ✅               |
| Projects                   |              | ✅          | ✅               |
| Vendor Bills               |              | ✅          | ✅               |
| Journal Entries            |              | ✅          | ✅               |
| Tax                        |              | ✅          | ✅               |
| Billable                   |              |            | ✅               |
| Location                   |              |            | ✅               |

---

# Step 1: Set up employees in QuickBooks Online

Before connecting, make sure employees are set up correctly in QuickBooks Online using the same email address they use in Expensify.

To do this:
1. Log into QuickBooks Online.
2. Go to **Payroll > Employees**.
3. Add or edit employees as needed (method may vary by country).

---

# Step 2: Connect Expensify to QuickBooks Online

To connect your workspace:

1. From the left-hand menu, go to **Workspaces**
2. Select your **Workspace**
3. Click **More features**
4. Scroll to **Integrate** and enable the **Accounting** toggle
5. Click **Accounting** in the left-hand menu
6. Click **Set up** next to **QuickBooks Online**
7. Log in with your Intuit credentials when prompted

This will enable the connection between QuickBooks Online and import your settings into Expensify.

![The toggle location to enable accounting integrations like QuickBooks Online]({{site.url}}/assets/images/ExpensifyHelp-QBO-1.png){:width="100%"}

![How to enable accounting integrations like QuickBooks Online]({{site.url}}/assets/images/ExpensifyHelp-QBO-2.png){:width="100%"}

![The QuickBooks Online Connect button]({{site.url}}/assets/images/ExpensifyHelp-QBO-3.png){:width="100%"}

![The QuickBooks Online Connect Accounting button]({{site.url}}/assets/images/ExpensifyHelp-QBO-4.png){:width="100%"}

![The QuickBooks Online Connect Connect button]({{site.url}}/assets/images/ExpensifyHelp-QBO-5.png){:width="100%"}

---

# FAQ 

# Why do I see a red dot next to the QuickBooks Online connection?

A red dot means there’s a connection error. You’ll see this:

- Next to **Accounting** in the left-hand menu
- On the QuickBooks Online connection card

This usually happens if the login details were entered incorrectly.

To fix it:

1. Click the **three-dot icon** next to the QuickBooks Online connection
2. Select **Enter credentials**
3. Enter your **Intuit login** again to reconnect

</div>
